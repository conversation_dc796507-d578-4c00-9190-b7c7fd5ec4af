import pandas as pd
import numpy as np
from datetime import datetime

def create_comprehensive_sop_report():
    """創建統整的SOP完整分析報告"""
    
    # 讀取數據
    try:
        df = pd.read_csv('sop_features_繁體中文.csv', encoding='utf-8-sig')
    except FileNotFoundError:
        print("找不到 sop_features_繁體中文.csv 檔案")
        return
    
    report_content = f"""
SOP姿態監控完整分析報告
{'='*100}
報告生成時間：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
資料來源：sop_features_繁體中文.csv
{'='*100}

本報告結合特徵定義說明與實際數據分析，提供完整的SOP監控解讀指南。

{'='*100}
第一部分：資料概覽
{'='*100}

基本資訊：
• 總幀數：{len(df):,} 幀
• 總時長：{df['時間_秒'].max():.2f} 秒 ({int(df['時間_秒'].max()//60):02d}:{int(df['時間_秒'].max()%60):02d})
• 平均幀率：{len(df) / df['時間_秒'].max():.2f} FPS
• 特徵數量：{len(df.columns)} 個
• 資料完整性：{(1 - df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100:.1f}%

{'='*100}
第二部分：特徵詳細定義與分析
{'='*100}

"""
    
    # 角度特徵分析
    report_content += """
🔧 2.1 角度特徵分析
────────────────────────────────────────────────────────────────────────────────

【左手肘角度 / 右手肘角度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：肩膀-手肘-手腕三點形成的角度                                        │
│ • 計算方式：使用向量夾角公式計算                                            │
│ • 數值範圍：0° - 180°                                                      │
│ • 正常範圍：90° - 150°                                                     │
│ • 判斷標準：< 90°(過度彎曲) | 90°-150°(正常) | >150°(過度伸展)             │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
"""
    
    # 計算角度統計
    left_elbow_mean = df['左手肘角度'].mean()
    left_elbow_min = df['左手肘角度'].min()
    left_elbow_max = df['左手肘角度'].max()
    left_elbow_normal = ((df['左手肘角度'] >= 90) & (df['左手肘角度'] <= 150)).sum()
    left_elbow_normal_pct = (left_elbow_normal / len(df)) * 100
    
    right_elbow_mean = df['右手肘角度'].mean()
    right_elbow_min = df['右手肘角度'].min()
    right_elbow_max = df['右手肘角度'].max()
    right_elbow_normal = ((df['右手肘角度'] >= 90) & (df['右手肘角度'] <= 150)).sum()
    right_elbow_normal_pct = (right_elbow_normal / len(df)) * 100
    
    report_content += f"""│ • 左手肘角度：平均 {left_elbow_mean:.1f}°，範圍 {left_elbow_min:.1f}° - {left_elbow_max:.1f}°                    │
│   └─ 正常範圍內：{left_elbow_normal_pct:.1f}% ({left_elbow_normal:,}/{len(df):,} 幀)                        │
│ • 右手肘角度：平均 {right_elbow_mean:.1f}°，範圍 {right_elbow_min:.1f}° - {right_elbow_max:.1f}°                    │
│   └─ 正常範圍內：{right_elbow_normal_pct:.1f}% ({right_elbow_normal:,}/{len(df):,} 幀)                        │
└────────────────────────────────────────────────────────────────────────────┘

【左手腕角度 / 右手腕角度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：手腕相對於手肘-肩膀參考線的旋轉角度                                 │
│ • 計算方式：計算手腕向量與參考向量的夾角                                    │
│ • 數值範圍：0° - 180°                                                      │
│ • 旋轉閾值：> 15°                                                          │
│ • 判斷標準：>15°(檢測到旋轉) | ≤15°(無明顯旋轉)                            │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
"""
    
    # 計算手腕角度統計
    left_wrist_mean = df['左手腕角度'].mean()
    left_wrist_rotation = (df['左手腕角度'] > 15).sum()
    left_wrist_rotation_pct = (left_wrist_rotation / len(df)) * 100
    
    right_wrist_mean = df['右手腕角度'].mean()
    right_wrist_rotation = (df['右手腕角度'] > 15).sum()
    right_wrist_rotation_pct = (right_wrist_rotation / len(df)) * 100
    
    report_content += f"""│ • 左手腕角度：平均 {left_wrist_mean:.1f}°                                              │
│   └─ 檢測到旋轉：{left_wrist_rotation_pct:.1f}% ({left_wrist_rotation:,}/{len(df):,} 幀)                      │
│ • 右手腕角度：平均 {right_wrist_mean:.1f}°                                              │
│   └─ 檢測到旋轉：{right_wrist_rotation_pct:.1f}% ({right_wrist_rotation:,}/{len(df):,} 幀)                      │
└────────────────────────────────────────────────────────────────────────────┘

"""
    
    # 速度特徵分析
    report_content += """
⚡ 2.2 速度特徵分析
────────────────────────────────────────────────────────────────────────────────

【左手腕速度 / 右手腕速度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：手腕移動的瞬時速度大小                                              │
│ • 計算方式：√[vₓ² + vᵧ² + vᵤ²]，其中v = Δ座標/Δ時間                        │
│ • 數值範圍：0.0 - 無上限                                                   │
│ • 正常範圍：0.01 - 0.5                                                     │
│ • 單位含義：標準化座標/秒 (1.0 ≈ 每秒橫跨整個畫面)                         │
│ • 判斷標準：<0.01(靜止) | 0.01-0.5(正常) | >0.5(過快)                      │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
"""
    
    # 計算速度統計
    left_speed_mean = df['左手腕速度'].mean()
    left_speed_max = df['左手腕速度'].max()
    left_speed_normal = ((df['左手腕速度'] >= 0.01) & (df['左手腕速度'] <= 0.5)).sum()
    left_speed_normal_pct = (left_speed_normal / len(df)) * 100
    left_speed_fast = (df['左手腕速度'] > 0.5).sum()
    left_speed_fast_pct = (left_speed_fast / len(df)) * 100
    
    right_speed_mean = df['右手腕速度'].mean()
    right_speed_max = df['右手腕速度'].max()
    right_speed_normal = ((df['右手腕速度'] >= 0.01) & (df['右手腕速度'] <= 0.5)).sum()
    right_speed_normal_pct = (right_speed_normal / len(df)) * 100
    right_speed_fast = (df['右手腕速度'] > 0.5).sum()
    right_speed_fast_pct = (right_speed_fast / len(df)) * 100
    
    report_content += f"""│ • 左手腕速度：平均 {left_speed_mean:.3f}，最高 {left_speed_max:.3f}                              │
│   ├─ 正常速度：{left_speed_normal_pct:.1f}% ({left_speed_normal:,}/{len(df):,} 幀)                          │
│   └─ 過快動作：{left_speed_fast_pct:.1f}% ({left_speed_fast:,}/{len(df):,} 幀)                            │
│ • 右手腕速度：平均 {right_speed_mean:.3f}，最高 {right_speed_max:.3f}                              │
│   ├─ 正常速度：{right_speed_normal_pct:.1f}% ({right_speed_normal:,}/{len(df):,} 幀)                          │
│   └─ 過快動作：{right_speed_fast_pct:.1f}% ({right_speed_fast:,}/{len(df):,} 幀)                            │
└────────────────────────────────────────────────────────────────────────────┘

"""
    
    # 姿勢特徵分析
    report_content += """
🧍 2.3 姿勢特徵分析
────────────────────────────────────────────────────────────────────────────────

【肩膀水平度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：左右肩膀Y座標差的絕對值                                             │
│ • 計算方式：|左肩Y座標 - 右肩Y座標|                                         │
│ • 數值範圍：0.0 - 1.0                                                      │
│ • 正常標準：< 0.1                                                          │
│ • 判斷標準：<0.1(肩膀水平) | ≥0.1(肩膀傾斜)                                │
└────────────────────────────────────────────────────────────────────────────┘

【身體前傾程度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：肩膀中心與臀部中心X座標差的絕對值                                   │
│ • 計算方式：|肩膀中心X - 臀部中心X|                                         │
│ • 數值範圍：0.0 - 1.0                                                      │
│ • 正常標準：< 0.2                                                          │
│ • 判斷標準：<0.2(正常姿勢) | ≥0.2(過度前傾)                                │
└────────────────────────────────────────────────────────────────────────────┘

【頭部位置】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：鼻子相對於肩膀中心的Y座標差                                         │
│ • 計算方式：鼻子Y座標 - 肩膀中心Y座標                                       │
│ • 數值範圍：-1.0 - 1.0                                                     │
│ • 正常標準：> -0.3                                                         │
│ • 判斷標準：>-0.3(頭部位置正常) | ≤-0.3(過度低頭)                          │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
"""
    
    # 計算姿勢統計
    shoulder_mean = df['肩膀水平度'].mean()
    shoulder_ok = (df['肩膀水平度'] < 0.1).sum()
    shoulder_ok_pct = (shoulder_ok / len(df)) * 100
    
    lean_mean = df['身體前傾程度'].mean()
    lean_ok = (df['身體前傾程度'] < 0.2).sum()
    lean_ok_pct = (lean_ok / len(df)) * 100
    
    head_mean = df['頭部位置'].mean()
    head_ok = (df['頭部位置'] > -0.3).sum()
    head_ok_pct = (head_ok / len(df)) * 100
    
    report_content += f"""│ • 肩膀水平度：平均 {shoulder_mean:.3f}                                              │
│   └─ 正常水平：{shoulder_ok_pct:.1f}% ({shoulder_ok:,}/{len(df):,} 幀)                            │
│ • 身體前傾程度：平均 {lean_mean:.3f}                                            │
│   └─ 正常姿勢：{lean_ok_pct:.1f}% ({lean_ok:,}/{len(df):,} 幀)                              │
│ • 頭部位置：平均 {head_mean:.3f}                                                │
│   └─ 位置正常：{head_ok_pct:.1f}% ({head_ok:,}/{len(df):,} 幀)                              │
└────────────────────────────────────────────────────────────────────────────┘

"""
    
    # SOP合規性分析
    avg_compliance = df['SOP合規性評分'].mean() if 'SOP合規性評分' in df.columns else 0
    min_compliance = df['SOP合規性評分'].min() if 'SOP合規性評分' in df.columns else 0
    max_compliance = df['SOP合規性評分'].max() if 'SOP合規性評分' in df.columns else 0
    
    report_content += f"""
📊 2.4 SOP合規性綜合分析
────────────────────────────────────────────────────────────────────────────────

【SOP合規性評分】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：所有檢查項目的通過比例                                              │
│ • 計算方式：通過項目數 ÷ 總檢查項目數                                       │
│ • 數值範圍：0.0 - 1.0                                                      │
│ • 評分等級：0.8+(優秀) | 0.6-0.8(良好) | 0.4-0.6(普通) | 0.0-0.4(需改進)   │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
│ • 平均評分：{avg_compliance:.3f} ({avg_compliance*100:.1f}%)                                        │
│ • 評分範圍：{min_compliance:.3f} - {max_compliance:.3f} ({min_compliance*100:.1f}% - {max_compliance*100:.1f}%)                              │
"""
    
    # 評分等級分佈
    if 'SOP合規性評分' in df.columns:
        excellent = (df['SOP合規性評分'] >= 0.8).sum()
        good = ((df['SOP合規性評分'] >= 0.6) & (df['SOP合規性評分'] < 0.8)).sum()
        fair = ((df['SOP合規性評分'] >= 0.4) & (df['SOP合規性評分'] < 0.6)).sum()
        poor = (df['SOP合規性評分'] < 0.4).sum()
        
        report_content += f"""│ • 評分分佈：                                                                │
│   ├─ 優秀 (≥80%)：{excellent:,} 幀 ({excellent/len(df)*100:.1f}%)                                │
│   ├─ 良好 (60-79%)：{good:,} 幀 ({good/len(df)*100:.1f}%)                              │
│   ├─ 普通 (40-59%)：{fair:,} 幀 ({fair/len(df)*100:.1f}%)                              │
│   └─ 需改進 (<40%)：{poor:,} 幀 ({poor/len(df)*100:.1f}%)                              │
└────────────────────────────────────────────────────────────────────────────┘

"""
    
    # 各項檢查詳細分析
    compliance_features = [
        ('手肘角度正常', '左右手肘角度都在90°-150°範圍內'),
        ('手腕旋轉正常', '左手腕角度>15° 或 右手腕角度>15°'),
        ('動作速度正常', '右手腕速度在0.01-0.5範圍內'),
        ('軌跡規律正常', '左手或右手軌跡規律性>0.3'),
        ('姿勢正常', '肩膀水平度<0.1 且 身體前傾程度<0.2'),
        ('頭部位置正常', '頭部位置>-0.3'),
        ('動作頻率正常', '右手動作頻率在10-60次/分鐘範圍內'),
        ('停頓時間正常', '右手停頓時間<5.0秒')
    ]
    
    report_content += """
┌─ 各項檢查詳細分析 ─────────────────────────────────────────────────────────┐
│ 檢查項目           │ 通過率   │ 通過幀數     │ 檢查標準                    │
├───────────────────┼─────────┼─────────────┼────────────────────────────┤
"""
    
    for feature, description in compliance_features:
        if feature in df.columns:
            pass_count = df[feature].sum()
            pass_rate = (pass_count / len(df)) * 100
            report_content += f"│ {feature:<15} │ {pass_rate:>6.1f}% │ {pass_count:>6,}/{len(df):,} │ {description:<26} │\n"
    
    report_content += """└───────────────────┴─────────┴─────────────┴────────────────────────────┘

"""
    
    return report_content

def main():
    try:
        content = create_comprehensive_sop_report()
        
        # 繼續添加其他部分...
        content += f"""
{'='*100}
第三部分：數值解讀範例
{'='*100}

以實際數據第一行為例：
151.82 | 130.45 | 162.35 | 128.63 | 0.500 | 0.480 | 0.402 | 2.009 | ...

詳細解讀：
┌─ 角度分析 ─────────────────────────────────────────────────────────────────┐
│ • 左手肘角度 151.82°：略微過度伸展（超出150°上限）❌                        │
│ • 右手肘角度 130.45°：在正常範圍內（90°-150°）✅                           │
│ • 左手腕角度 162.35°：檢測到明顯旋轉動作（>15°）✅                         │
│ • 右手腕角度 128.63°：檢測到旋轉動作（>15°）✅                             │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 動作分析 ─────────────────────────────────────────────────────────────────┐
│ • 左臂伸展程度 0.500：中等伸展程度                                         │
│ • 右臂伸展程度 0.480：中等伸展程度                                         │
│ • 左手腕速度 0.402：接近正常上限（0.5），動作稍快                          │
│ • 右手腕速度 2.009：明顯過快（超出0.5上限4倍）❌                           │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 合規性分析 ───────────────────────────────────────────────────────────────┐
│ • 最終SOP評分：0.375 (37.5%)                                              │
│ • 評分等級：需改進（<40%）                                                 │
│ • 通過項目：3/8 項                                                        │
│ • 主要問題：手肘角度、動作速度、姿勢需要改進                               │
└────────────────────────────────────────────────────────────────────────────┘

{'='*100}
第四部分：問題診斷與建議
{'='*100}

🚨 發現的主要問題：
"""
        
        # 讀取數據進行問題分析
        try:
            df = pd.read_csv('sop_features_繁體中文.csv', encoding='utf-8-sig')
            
            problems = []
            if '手肘角度正常' in df.columns:
                elbow_pass_rate = df['手肘角度正常'].mean()
                if elbow_pass_rate < 0.5:
                    problems.append(f"1. 手肘角度問題：通過率僅 {elbow_pass_rate*100:.1f}%")
                    
            if '動作速度正常' in df.columns:
                speed_pass_rate = df['動作速度正常'].mean()
                if speed_pass_rate < 0.5:
                    problems.append(f"2. 動作速度問題：通過率僅 {speed_pass_rate*100:.1f}%")
                    
            if '姿勢正常' in df.columns:
                posture_pass_rate = df['姿勢正常'].mean()
                if posture_pass_rate < 0.5:
                    problems.append(f"3. 姿勢問題：通過率僅 {posture_pass_rate*100:.1f}%")
            
            if problems:
                for problem in problems:
                    content += f"{problem}\n"
            else:
                content += "• 整體表現良好，各項指標大多符合標準\n"
                
        except:
            content += "• 無法讀取數據進行問題分析\n"
        
        content += f"""
💡 改進建議：

1. 【閾值調整建議】
   • 速度閾值：考慮將上限從0.5調整為1.0-2.0
   • 肩膀水平度：考慮將標準從0.1放寬至0.15-0.2
   • 手肘角度：可考慮將範圍擴大為85°-155°

2. 【系統優化建議】
   • 增加資料平滑處理，減少檢測誤差
   • 建立個人化基準線，適應不同工作者
   • 加入時間窗口分析，避免瞬時異常影響整體評分

3. 【監控策略建議】
   • 重點關注持續性問題，而非瞬時異常
   • 結合影片回放驗證異常檢測的準確性
   • 建立趨勢分析，追蹤長期改進效果

4. 【實際應用建議】
   • 根據具體工作環境調整標準
   • 定期校準檢測系統
   • 建立工作者培訓回饋機制

{'='*100}
第五部分：技術說明
{'='*100}

📋 重要技術說明：

1. 【座標系統】
   • 所有座標都是標準化的（0-1範圍）
   • 不是真實物理尺寸，需要結合實際畫面理解

2. 【速度計算】
   • 單位：標準化座標/秒
   • 1.0 ≈ 每秒橫跨整個畫面
   • 實際物理速度需要結合攝影機參數計算

3. 【角度計算】
   • 使用3D向量夾角公式
   • 角度範圍：0°-180°
   • 計算基於MediaPipe檢測的關鍵點

4. 【資料品質】
   • 空白值：計算所需歷史資料不足
   • 0值：無檢測到該特徵或初始狀態
   • TRUE/FALSE：基於預設閾值的自動判斷

5. 【使用限制】
   • 閾值可能需要根據實際環境調整
   • 檢測精度受光照、角度、遮擋影響
   • 建議結合人工驗證使用

{'='*100}
報告結束
{'='*100}
生成時間：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
資料來源：sop_features_繁體中文.csv
報告版本：v1.0 統整版
{'='*100}
"""
        
        # 保存報告
        with open('SOP完整分析報告_統整版.txt', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("統整版完整分析報告已生成：SOP完整分析報告_統整版.txt")
        print("此報告包含：")
        print("• 特徵定義說明")
        print("• 實際數據分析")
        print("• 數值解讀範例")
        print("• 問題診斷與建議")
        print("• 技術說明")
        
    except Exception as e:
        print(f"生成報告時發生錯誤：{e}")

if __name__ == "__main__":
    main()
