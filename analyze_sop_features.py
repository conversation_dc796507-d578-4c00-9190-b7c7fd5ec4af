import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 設定中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SOPFeaturesAnalyzer:
    def __init__(self, csv_file):
        self.df = pd.read_csv(csv_file, encoding='utf-8-sig')
        self.report_content = []
        
    def add_section(self, title, content):
        """添加報告章節"""
        self.report_content.append(f"\n{'='*60}")
        self.report_content.append(f"{title}")
        self.report_content.append(f"{'='*60}")
        self.report_content.append(content)
        
    def analyze_basic_info(self):
        """基本資訊分析"""
        total_frames = len(self.df)
        total_time = self.df['時間_秒'].max()
        fps = total_frames / total_time if total_time > 0 else 0
        
        content = f"""
資料基本資訊：
• 總幀數：{total_frames:,} 幀
• 總時長：{total_time:.2f} 秒 ({int(total_time//60):02d}:{int(total_time%60):02d})
• 平均幀率：{fps:.2f} FPS
• 特徵數量：{len(self.df.columns)} 個
• 資料完整性：{(1 - self.df.isnull().sum().sum() / (len(self.df) * len(self.df.columns))) * 100:.1f}%
        """
        self.add_section("1. 資料基本資訊", content)
        
    def analyze_angles(self):
        """角度特徵分析"""
        angle_features = ['左手肘角度', '右手肘角度', '左手腕角度', '右手腕角度']
        
        content = "\n角度特徵統計：\n"
        content += f"{'特徵名稱':<12} {'平均值':<8} {'最小值':<8} {'最大值':<8} {'標準差':<8} {'正常範圍':<15}\n"
        content += "-" * 70 + "\n"
        
        for feature in angle_features:
            if feature in self.df.columns:
                mean_val = self.df[feature].mean()
                min_val = self.df[feature].min()
                max_val = self.df[feature].max()
                std_val = self.df[feature].std()
                
                if '手肘' in feature:
                    normal_range = "90-150度"
                else:
                    normal_range = ">15度(旋轉)"
                    
                content += f"{feature:<12} {mean_val:<8.1f} {min_val:<8.1f} {max_val:<8.1f} {std_val:<8.1f} {normal_range:<15}\n"
        
        # 角度分佈分析
        content += "\n角度分佈分析：\n"
        for feature in angle_features:
            if feature in self.df.columns:
                if '手肘' in feature:
                    normal_count = ((self.df[feature] >= 90) & (self.df[feature] <= 150)).sum()
                    normal_pct = (normal_count / len(self.df)) * 100
                    content += f"• {feature}：{normal_pct:.1f}% 在正常範圍內 (90-150度)\n"
                else:
                    rotation_count = (self.df[feature] > 15).sum()
                    rotation_pct = (rotation_count / len(self.df)) * 100
                    content += f"• {feature}：{rotation_pct:.1f}% 檢測到旋轉動作 (>15度)\n"
                    
        self.add_section("2. 角度特徵分析", content)
        
    def analyze_speed_and_movement(self):
        """速度和動作分析"""
        speed_features = ['左手腕速度', '右手腕速度']
        movement_features = ['左臂伸展程度', '右臂伸展程度', '雙手距離']
        
        content = "\n速度特徵統計：\n"
        content += f"{'特徵名稱':<12} {'平均值':<8} {'最小值':<8} {'最大值':<8} {'標準差':<8} {'正常範圍':<15}\n"
        content += "-" * 70 + "\n"
        
        for feature in speed_features:
            if feature in self.df.columns:
                mean_val = self.df[feature].mean()
                min_val = self.df[feature].min()
                max_val = self.df[feature].max()
                std_val = self.df[feature].std()
                normal_range = "0.01-0.5"
                
                content += f"{feature:<12} {mean_val:<8.3f} {min_val:<8.3f} {max_val:<8.3f} {std_val:<8.3f} {normal_range:<15}\n"
        
        # 速度分佈分析
        content += "\n速度分佈分析：\n"
        for feature in speed_features:
            if feature in self.df.columns:
                normal_speed = ((self.df[feature] >= 0.01) & (self.df[feature] <= 0.5)).sum()
                normal_pct = (normal_speed / len(self.df)) * 100
                too_fast = (self.df[feature] > 0.5).sum()
                too_fast_pct = (too_fast / len(self.df)) * 100
                too_slow = (self.df[feature] < 0.01).sum()
                too_slow_pct = (too_slow / len(self.df)) * 100
                
                content += f"• {feature}：\n"
                content += f"  - 正常速度：{normal_pct:.1f}% (0.01-0.5)\n"
                content += f"  - 過快：{too_fast_pct:.1f}% (>0.5)\n"
                content += f"  - 過慢：{too_slow_pct:.1f}% (<0.01)\n"
        
        # 動作範圍分析
        content += "\n動作範圍統計：\n"
        for feature in movement_features:
            if feature in self.df.columns:
                mean_val = self.df[feature].mean()
                min_val = self.df[feature].min()
                max_val = self.df[feature].max()
                content += f"• {feature}：平均 {mean_val:.3f}，範圍 {min_val:.3f} - {max_val:.3f}\n"
                
        self.add_section("3. 速度與動作分析", content)
        
    def analyze_posture(self):
        """姿勢分析"""
        posture_features = ['肩膀水平度', '身體前傾程度', '頭部位置']
        
        content = "\n姿勢特徵統計：\n"
        content += f"{'特徵名稱':<12} {'平均值':<8} {'最小值':<8} {'最大值':<8} {'標準差':<8} {'正常標準':<15}\n"
        content += "-" * 70 + "\n"
        
        standards = {
            '肩膀水平度': '<0.1',
            '身體前傾程度': '<0.2', 
            '頭部位置': '>-0.3'
        }
        
        for feature in posture_features:
            if feature in self.df.columns:
                mean_val = self.df[feature].mean()
                min_val = self.df[feature].min()
                max_val = self.df[feature].max()
                std_val = self.df[feature].std()
                standard = standards.get(feature, "未定義")
                
                content += f"{feature:<12} {mean_val:<8.3f} {min_val:<8.3f} {max_val:<8.3f} {std_val:<8.3f} {standard:<15}\n"
        
        # 姿勢合規性分析
        content += "\n姿勢合規性分析：\n"
        if '肩膀水平度' in self.df.columns:
            shoulder_ok = (self.df['肩膀水平度'] < 0.1).sum()
            shoulder_pct = (shoulder_ok / len(self.df)) * 100
            content += f"• 肩膀水平正常：{shoulder_pct:.1f}% (<0.1)\n"
            
        if '身體前傾程度' in self.df.columns:
            lean_ok = (self.df['身體前傾程度'] < 0.2).sum()
            lean_pct = (lean_ok / len(self.df)) * 100
            content += f"• 身體前傾正常：{lean_pct:.1f}% (<0.2)\n"
            
        if '頭部位置' in self.df.columns:
            head_ok = (self.df['頭部位置'] > -0.3).sum()
            head_pct = (head_ok / len(self.df)) * 100
            content += f"• 頭部位置正常：{head_pct:.1f}% (>-0.3，避免過度低頭)\n"
            
        self.add_section("4. 姿勢分析", content)
        
    def analyze_compliance(self):
        """SOP合規性分析"""
        compliance_features = [
            '手肘角度正常', '手腕旋轉正常', '動作速度正常', '軌跡規律正常',
            '姿勢正常', '頭部位置正常', '動作頻率正常', '停頓時間正常'
        ]
        
        content = "\nSOP合規性統計：\n"
        content += f"{'檢查項目':<12} {'通過率':<8} {'通過幀數':<10} {'總幀數':<8}\n"
        content += "-" * 50 + "\n"
        
        for feature in compliance_features:
            if feature in self.df.columns:
                pass_count = self.df[feature].sum()
                pass_rate = (pass_count / len(self.df)) * 100
                content += f"{feature:<12} {pass_rate:<8.1f}% {pass_count:<10} {len(self.df):<8}\n"
        
        # 整體合規性評分
        if 'SOP合規性評分' in self.df.columns:
            avg_score = self.df['SOP合規性評分'].mean()
            min_score = self.df['SOP合規性評分'].min()
            max_score = self.df['SOP合規性評分'].max()
            
            content += f"\n整體SOP合規性評分：\n"
            content += f"• 平均評分：{avg_score:.3f} ({avg_score*100:.1f}%)\n"
            content += f"• 最低評分：{min_score:.3f} ({min_score*100:.1f}%)\n"
            content += f"• 最高評分：{max_score:.3f} ({max_score*100:.1f}%)\n"
            
            # 評分等級分佈
            excellent = (self.df['SOP合規性評分'] >= 0.8).sum()
            good = ((self.df['SOP合規性評分'] >= 0.6) & (self.df['SOP合規性評分'] < 0.8)).sum()
            fair = ((self.df['SOP合規性評分'] >= 0.4) & (self.df['SOP合規性評分'] < 0.6)).sum()
            poor = (self.df['SOP合規性評分'] < 0.4).sum()
            
            content += f"\n評分等級分佈：\n"
            content += f"• 優秀 (≥80%)：{excellent} 幀 ({excellent/len(self.df)*100:.1f}%)\n"
            content += f"• 良好 (60-79%)：{good} 幀 ({good/len(self.df)*100:.1f}%)\n"
            content += f"• 普通 (40-59%)：{fair} 幀 ({fair/len(self.df)*100:.1f}%)\n"
            content += f"• 需改進 (<40%)：{poor} 幀 ({poor/len(self.df)*100:.1f}%)\n"
            
        self.add_section("5. SOP合規性分析", content)
        
    def analyze_work_rhythm(self):
        """工作節奏分析"""
        rhythm_features = ['左手動作頻率', '右手動作頻率', '左手停頓時間', '右手停頓時間', '工作節奏']
        
        content = "\n工作節奏特徵統計：\n"
        
        for feature in rhythm_features:
            if feature in self.df.columns:
                if '頻率' in feature:
                    mean_val = self.df[feature].mean()
                    content += f"• {feature}：平均 {mean_val:.1f} 次/分鐘\n"
                elif '停頓' in feature:
                    mean_val = self.df[feature].mean()
                    max_val = self.df[feature].max()
                    content += f"• {feature}：平均 {mean_val:.2f} 秒，最長 {max_val:.2f} 秒\n"
                elif feature == '工作節奏':
                    rhythm_count = self.df[feature].sum()
                    content += f"• 檢測到動作峰值：{rhythm_count} 次\n"
        
        self.add_section("6. 工作節奏分析", content)
        
    def generate_summary(self):
        """生成總結"""
        avg_compliance = self.df['SOP合規性評分'].mean() if 'SOP合規性評分' in self.df.columns else 0
        
        # 找出主要問題
        problems = []
        if '手肘角度正常' in self.df.columns:
            elbow_pass_rate = self.df['手肘角度正常'].mean()
            if elbow_pass_rate < 0.5:
                problems.append(f"手肘角度問題 (通過率: {elbow_pass_rate*100:.1f}%)")
                
        if '動作速度正常' in self.df.columns:
            speed_pass_rate = self.df['動作速度正常'].mean()
            if speed_pass_rate < 0.5:
                problems.append(f"動作速度問題 (通過率: {speed_pass_rate*100:.1f}%)")
                
        if '姿勢正常' in self.df.columns:
            posture_pass_rate = self.df['姿勢正常'].mean()
            if posture_pass_rate < 0.5:
                problems.append(f"姿勢問題 (通過率: {posture_pass_rate*100:.1f}%)")
        
        content = f"""
分析總結：

整體表現：
• SOP合規性平均評分：{avg_compliance:.3f} ({avg_compliance*100:.1f}%)
• 資料時長：{self.df['時間_秒'].max():.1f} 秒
• 分析幀數：{len(self.df):,} 幀

主要發現：
"""
        
        if problems:
            content += "需要改進的項目：\n"
            for i, problem in enumerate(problems, 1):
                content += f"{i}. {problem}\n"
        else:
            content += "• 整體表現良好，各項指標大多符合標準\n"
            
        content += f"""
建議：
1. 針對通過率較低的項目進行重點改進
2. 可考慮調整閾值標準以符合實際工作需求
3. 建議進行更長時間的監控以獲得更全面的分析
4. 可結合影片回放來驗證異常檢測的準確性

報告生成時間：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        self.add_section("7. 分析總結與建議", content)
        
    def save_report(self, filename='SOP特徵分析報告.txt'):
        """保存報告"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("SOP姿態特徵詳細分析報告\n")
            f.write("=" * 60 + "\n")
            f.write(f"報告生成時間：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n")
            f.write("=" * 60 + "\n")
            
            for content in self.report_content:
                f.write(content + "\n")
                
        print(f"詳細分析報告已保存為：{filename}")
        
    def run_full_analysis(self):
        """執行完整分析"""
        print("正在進行SOP特徵詳細分析...")
        
        self.analyze_basic_info()
        self.analyze_angles()
        self.analyze_speed_and_movement()
        self.analyze_posture()
        self.analyze_compliance()
        self.analyze_work_rhythm()
        self.generate_summary()
        
        self.save_report()
        
        print("分析完成！")

def main():
    try:
        analyzer = SOPFeaturesAnalyzer('sop_features_繁體中文.csv')
        analyzer.run_full_analysis()
    except FileNotFoundError:
        print("找不到 'sop_features_繁體中文.csv' 檔案")
        print("請確認檔案是否存在於當前目錄中")
    except Exception as e:
        print(f"分析過程中發生錯誤：{e}")

if __name__ == "__main__":
    main()
