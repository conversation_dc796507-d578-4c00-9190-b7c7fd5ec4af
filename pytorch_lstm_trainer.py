import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
from collections import Counter
import time

# 設置設備
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用設備: {device}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")

class SOPDataset(Dataset):
    """SOP資料集類別"""
    def __init__(self, sequences, labels):
        self.sequences = torch.FloatTensor(sequences)
        self.labels = torch.LongTensor(labels)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.labels[idx]

class SOPLSTM(nn.Module):
    """SOP LSTM模型"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes, dropout=0.2):
        super(SOPLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM層
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=False
        )
        
        # 全連接層
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.dropout = nn.Dropout(dropout)
        self.fc2 = nn.Linear(hidden_size // 2, num_classes)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # LSTM前向傳播
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 取最後一個時間步的輸出
        last_output = lstm_out[:, -1, :]
        
        # 全連接層
        out = self.fc1(last_output)
        out = self.relu(out)
        out = self.dropout(out)
        out = self.fc2(out)
        
        return out

class PyTorchSOPTrainer:
    def __init__(self, csv_file='sop_features_已標記_繁體中文.csv'):
        self.csv_file = csv_file
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.model = None
        self.sequence_length = 15  # 15幀作為一個序列
        self.device = device
        
    def load_and_preprocess_data(self):
        """載入和預處理資料"""
        print("正在載入資料...")
        df = pd.read_csv(self.csv_file, encoding='utf-8-sig')
        
        print(f"原始資料形狀: {df.shape}")
        print(f"標籤分佈:")
        label_counts = df['標籤'].value_counts()
        for label, count in label_counts.items():
            print(f"  {label}: {count} 行")
        
        # 保留所有標籤
        df_filtered = df.copy()
        print(f"保留所有標籤，資料形狀: {df_filtered.shape}")
        
        # 選擇特徵欄位
        feature_columns = []
        exclude_columns = ['幀數', '時間_秒', '標籤', '時間_分秒']
        
        for col in df_filtered.columns:
            if col not in exclude_columns:
                if df_filtered[col].dtype in ['int64', 'float64', 'bool']:
                    feature_columns.append(col)
                elif col.endswith('正常'):
                    df_filtered[col] = df_filtered[col].astype(int)
                    feature_columns.append(col)
        
        print(f"選擇的特徵欄位數量: {len(feature_columns)}")
        
        # 處理缺失值
        df_filtered[feature_columns] = df_filtered[feature_columns].fillna(0)
        
        return df_filtered, feature_columns
    
    def create_sequences(self, df, feature_columns):
        """創建時間序列資料"""
        print("正在創建時間序列...")
        
        X, y = [], []
        
        # 按標籤分組創建序列
        current_label = None
        current_sequence = []
        
        for idx, row in df.iterrows():
            label = row['標籤']
            features = row[feature_columns].values
            
            if label != current_label:
                # 處理之前的序列
                if len(current_sequence) >= self.sequence_length:
                    # 滑動窗口
                    for i in range(len(current_sequence) - self.sequence_length + 1):
                        X.append(current_sequence[i:i + self.sequence_length])
                        y.append(current_label)
                elif len(current_sequence) >= 5:  # 至少5幀
                    # 填充到所需長度
                    padded_sequence = current_sequence.copy()
                    while len(padded_sequence) < self.sequence_length:
                        padded_sequence.append(current_sequence[-1])
                    X.append(padded_sequence[:self.sequence_length])
                    y.append(current_label)
                
                # 開始新序列
                current_label = label
                current_sequence = [features]
            else:
                current_sequence.append(features)
        
        # 處理最後一個序列
        if len(current_sequence) >= self.sequence_length:
            for i in range(len(current_sequence) - self.sequence_length + 1):
                X.append(current_sequence[i:i + self.sequence_length])
                y.append(current_label)
        elif len(current_sequence) >= 5:
            padded_sequence = current_sequence.copy()
            while len(padded_sequence) < self.sequence_length:
                padded_sequence.append(current_sequence[-1])
            X.append(padded_sequence[:self.sequence_length])
            y.append(current_label)
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"創建的序列數量: {len(X)}")
        print(f"序列形狀: {X.shape}")
        print(f"標籤分佈:")
        label_counts = Counter(y)
        for label, count in label_counts.items():
            print(f"  {label}: {count} 序列")
        
        return X, y
    
    def prepare_data(self):
        """準備訓練資料"""
        # 載入資料
        df, feature_columns = self.load_and_preprocess_data()
        
        # 創建序列
        X, y = self.create_sequences(df, feature_columns)
        
        if len(X) == 0:
            raise ValueError("沒有足夠的資料創建序列")
        
        # 標準化特徵
        print("正在標準化特徵...")
        X_reshaped = X.reshape(-1, X.shape[-1])
        X_scaled = self.scaler.fit_transform(X_reshaped)
        X = X_scaled.reshape(X.shape)
        
        # 編碼標籤
        print("正在編碼標籤...")
        y_encoded = self.label_encoder.fit_transform(y)
        
        # 分割資料
        unique_labels, label_counts = np.unique(y_encoded, return_counts=True)
        min_count = np.min(label_counts)
        
        if min_count == 1:
            print("警告：某些標籤只有1個樣本，將不使用分層抽樣")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y_encoded, test_size=0.2, random_state=42
            )
        else:
            X_train, X_test, y_train, y_test = train_test_split(
                X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
            )
        
        print(f"訓練集形狀: {X_train.shape}")
        print(f"測試集形狀: {X_test.shape}")
        
        return X_train, X_test, y_train, y_test, feature_columns
    
    def create_data_loaders(self, X_train, X_test, y_train, y_test, batch_size=32):
        """創建資料載入器"""
        train_dataset = SOPDataset(X_train, y_train)
        test_dataset = SOPDataset(X_test, y_test)
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        return train_loader, test_loader
    
    def build_model(self, input_size, num_classes):
        """建立模型"""
        model = SOPLSTM(
            input_size=input_size,
            hidden_size=128,
            num_layers=2,
            num_classes=num_classes,
            dropout=0.3
        ).to(self.device)
        
        print("模型架構:")
        print(model)
        
        # 計算參數數量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"總參數數量: {total_params:,}")
        print(f"可訓練參數數量: {trainable_params:,}")
        
        return model
    
    def train_model(self, train_loader, test_loader, num_classes, input_size, epochs=50):
        """訓練模型"""
        print("正在建立和訓練模型...")
        
        # 建立模型
        self.model = self.build_model(input_size, num_classes)
        
        # 損失函數和優化器
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=0.001, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
        
        # 訓練歷史
        train_losses = []
        train_accuracies = []
        val_losses = []
        val_accuracies = []
        
        best_val_acc = 0
        patience_counter = 0
        patience = 10
        
        print(f"開始訓練 (設備: {self.device})...")
        start_time = time.time()
        
        for epoch in range(epochs):
            # 訓練階段
            self.model.train()
            train_loss = 0
            train_correct = 0
            train_total = 0
            
            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(self.device), target.to(self.device)
                
                optimizer.zero_grad()
                output = self.model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                train_total += target.size(0)
                train_correct += (predicted == target).sum().item()
            
            # 驗證階段
            self.model.eval()
            val_loss = 0
            val_correct = 0
            val_total = 0
            
            with torch.no_grad():
                for data, target in test_loader:
                    data, target = data.to(self.device), target.to(self.device)
                    output = self.model(data)
                    loss = criterion(output, target)
                    
                    val_loss += loss.item()
                    _, predicted = torch.max(output.data, 1)
                    val_total += target.size(0)
                    val_correct += (predicted == target).sum().item()
            
            # 計算平均值
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(test_loader)
            train_acc = 100 * train_correct / train_total
            val_acc = 100 * val_correct / val_total
            
            # 記錄歷史
            train_losses.append(avg_train_loss)
            train_accuracies.append(train_acc)
            val_losses.append(avg_val_loss)
            val_accuracies.append(val_acc)
            
            # 學習率調整
            scheduler.step(avg_val_loss)
            
            # 早停檢查
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_sop_pytorch_model.pth')
            else:
                patience_counter += 1
            
            # 打印進度
            if (epoch + 1) % 5 == 0 or epoch == 0:
                elapsed_time = time.time() - start_time
                print(f'Epoch [{epoch+1}/{epochs}] ({elapsed_time:.1f}s)')
                print(f'  訓練 - 損失: {avg_train_loss:.4f}, 準確率: {train_acc:.2f}%')
                print(f'  驗證 - 損失: {avg_val_loss:.4f}, 準確率: {val_acc:.2f}%')
                print(f'  學習率: {optimizer.param_groups[0]["lr"]:.6f}')
            
            # 早停
            if patience_counter >= patience:
                print(f"早停於第 {epoch+1} epoch (最佳驗證準確率: {best_val_acc:.2f}%)")
                break
        
        # 載入最佳模型
        self.model.load_state_dict(torch.load('best_sop_pytorch_model.pth'))
        
        total_time = time.time() - start_time
        print(f"訓練完成！總時間: {total_time:.1f}秒")
        print(f"最佳驗證準確率: {best_val_acc:.2f}%")
        
        return {
            'train_losses': train_losses,
            'train_accuracies': train_accuracies,
            'val_losses': val_losses,
            'val_accuracies': val_accuracies
        }
    
    def evaluate_model(self, test_loader):
        """評估模型"""
        print("正在評估模型...")
        
        self.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                _, predicted = torch.max(output, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
        
        # 計算準確率
        accuracy = accuracy_score(all_targets, all_predictions)
        print(f"測試集準確率: {accuracy:.4f}")
        
        # 分類報告
        target_names = self.label_encoder.classes_
        print("\n分類報告:")
        print(classification_report(all_targets, all_predictions, 
                                  target_names=target_names, zero_division=0))
        
        # 混淆矩陣
        cm = confusion_matrix(all_targets, all_predictions)
        plt.figure(figsize=(15, 12))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=target_names, yticklabels=target_names)
        plt.title('PyTorch LSTM 混淆矩陣')
        plt.ylabel('真實標籤')
        plt.xlabel('預測標籤')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig('pytorch_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return accuracy
    
    def save_model_and_encoders(self):
        """保存模型和編碼器"""
        print("正在保存模型和編碼器...")
        
        # 保存模型
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'model_config': {
                'input_size': self.model.lstm.input_size,
                'hidden_size': self.model.hidden_size,
                'num_layers': self.model.num_layers,
                'num_classes': self.model.fc2.out_features
            }
        }, 'sop_pytorch_model.pth')
        
        # 保存編碼器
        joblib.dump(self.label_encoder, 'label_encoder_pytorch.pkl')
        joblib.dump(self.scaler, 'feature_scaler_pytorch.pkl')
        
        print("模型已保存為: sop_pytorch_model.pth")
        print("標籤編碼器已保存為: label_encoder_pytorch.pkl")
        print("特徵縮放器已保存為: feature_scaler_pytorch.pkl")
    
    def plot_training_history(self, history):
        """繪製訓練歷史"""
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 3, 1)
        plt.plot(history['train_losses'], label='訓練損失')
        plt.plot(history['val_losses'], label='驗證損失')
        plt.title('模型損失')
        plt.xlabel('Epoch')
        plt.ylabel('損失')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(1, 3, 2)
        plt.plot(history['train_accuracies'], label='訓練準確率')
        plt.plot(history['val_accuracies'], label='驗證準確率')
        plt.title('模型準確率')
        plt.xlabel('Epoch')
        plt.ylabel('準確率 (%)')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(1, 3, 3)
        plt.plot(history['val_accuracies'], 'g-', linewidth=2)
        plt.title('驗證準確率趨勢')
        plt.xlabel('Epoch')
        plt.ylabel('驗證準確率 (%)')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('pytorch_training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def train(self):
        """完整的訓練流程"""
        try:
            print("=" * 60)
            print("PyTorch SOP LSTM 訓練器")
            print("=" * 60)
            
            # 準備資料
            X_train, X_test, y_train, y_test, feature_columns = self.prepare_data()
            
            # 創建資料載入器
            train_loader, test_loader = self.create_data_loaders(X_train, X_test, y_train, y_test)
            
            # 訓練模型
            input_size = X_train.shape[2]
            num_classes = len(np.unique(y_train))
            history = self.train_model(train_loader, test_loader, num_classes, input_size)
            
            # 評估模型
            accuracy = self.evaluate_model(test_loader)
            
            # 保存模型
            self.save_model_and_encoders()
            
            # 繪製訓練歷史
            self.plot_training_history(history)
            
            print("=" * 60)
            print(f"🎉 訓練完成！最終測試準確率: {accuracy:.4f}")
            print("=" * 60)
            
        except Exception as e:
            print(f"訓練過程中發生錯誤: {e}")
            import traceback
            traceback.print_exc()

def main():
    trainer = PyTorchSOPTrainer()
    trainer.train()

if __name__ == "__main__":
    main()
