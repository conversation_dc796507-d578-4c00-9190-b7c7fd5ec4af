# PyTorch LSTM 模型摘要

## 📊 模型基本資訊
- **模型類型**: LSTM (長短期記憶網路)
- **深度學習框架**: PyTorch 2.7.1
- **訓練設備**: NVIDIA GeForce GTX 1660 Ti (GPU加速)
- **最終測試準確率**: 66.67%

## 🏷️ 標籤資訊
- **標籤總數**: 17 個類別
- **標籤分佈**:

| 編號 | 標籤名稱 | 資料量 |
|------|----------|--------|
|  1 | 待機 | 507 行 |
|  2 | 旋轉工件(順時針) | 106 行 |
|  3 | 結束動作 | 30 行 |
|  4 | 鎖第10顆螺絲 | 30 行 |
|  5 | 鎖第11顆螺絲 | 60 行 |
|  6 | 鎖第13顆螺絲 | 60 行 |
|  7 | 鎖第14顆螺絲 | 30 行 |
|  8 | 鎖第15顆螺絲 | 30 行 |
|  9 | 鎖第1顆螺絲 | 70 行 |
| 10 | 鎖第2顆螺絲 | 30 行 |
| 11 | 鎖第3顆螺絲 | 60 行 |
| 12 | 鎖第4顆螺絲 | 30 行 |
| 13 | 鎖第5顆螺絲 | 60 行 |
| 14 | 鎖第6顆螺絲 | 60 行 |
| 15 | 鎖第7顆螺絲 | 30 行 |
| 16 | 鎖第8顆螺絲 | 60 行 |
| 17 | 鎖第9顆螺絲 | 60 行 |

## 📈 資料統計
- **原始資料總量**: 1,315 行
- **特徵維度**: 32 個特徵
- **序列長度**: 10 幀
- **模型參數**: 224,401 個

## 📁 檔案說明

### 模型檔案
1. **`sop_pytorch_model.pth`** - 完整的PyTorch模型檔案
2. **`best_sop_pytorch_model.pth`** - 最佳模型權重
3. **`label_encoder_pytorch.pkl`** - 標籤編碼器
4. **`feature_scaler_pytorch.pkl`** - 特徵縮放器

### 視覺化檔案
5. **`混淆矩陣_繁體中文.png`** - 模型預測效果圖（支援繁體中文）
6. **`訓練歷史_繁體中文.png`** - 訓練過程圖（支援繁體中文）

## 🚀 使用方法
```python
import torch
import joblib

# 載入模型
model = torch.load('pytorch_models/sop_pytorch_model.pth')
label_encoder = joblib.load('pytorch_models/label_encoder_pytorch.pkl')
scaler = joblib.load('pytorch_models/feature_scaler_pytorch.pkl')

# 進行預測
model.eval()
# predictions = model(input_data)
```

生成時間: 2025年07月11日 11:27:56
