import pandas as pd
import cv2
import os

def get_video_fps(video_path):
    """獲取影片的幀率"""
    try:
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        cap.release()
        return fps
    except Exception as e:
        print(f"無法讀取影片 {video_path}: {e}")
        return None

def add_time_to_csv():
    # 檢查可能的影片檔案
    video_files = ['1.mp4', '1_processed.mp4']
    fps = None
    
    for video_file in video_files:
        if os.path.exists(video_file):
            fps = get_video_fps(video_file)
            if fps:
                print(f"從 {video_file} 獲取幀率: {fps:.2f} FPS")
                break
    
    if not fps:
        # 如果無法獲取幀率，使用常見的預設值
        fps = 30.0  # 預設30 FPS
        print(f"無法獲取影片幀率，使用預設值: {fps} FPS")
        print("如果您知道正確的幀率，請手動修改此值")
    
    try:
        # 讀取CSV檔案
        csv_files = ['pose_data_繁體中文.csv', 'pose_data.csv']
        df = None
        used_file = None
        
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                used_file = csv_file
                print(f"讀取CSV檔案: {csv_file}")
                break
        
        if df is None:
            print("找不到CSV檔案")
            return
        
        # 檢查幀數欄位名稱
        frame_column = None
        possible_frame_columns = ['幀數', 'frame', '幀', 'Frame']
        
        for col in possible_frame_columns:
            if col in df.columns:
                frame_column = col
                break
        
        if frame_column is None:
            print("找不到幀數欄位")
            print("可用的欄位:", list(df.columns))
            return
        
        print(f"使用幀數欄位: {frame_column}")
        
        # 計算時間（秒）
        df['時間_秒'] = df[frame_column] / fps
        
        # 計算時間（分:秒格式）
        df['時間_分秒'] = df['時間_秒'].apply(lambda x: f"{int(x//60):02d}:{int(x%60):02d}.{int((x%1)*100):02d}")
        
        # 重新排列欄位，將時間欄位放在前面
        columns = list(df.columns)
        # 移除時間欄位
        columns.remove('時間_秒')
        columns.remove('時間_分秒')
        
        # 在幀數後面插入時間欄位
        frame_index = columns.index(frame_column)
        columns.insert(frame_index + 1, '時間_秒')
        columns.insert(frame_index + 2, '時間_分秒')
        
        df = df[columns]
        
        # 保存新的CSV檔案
        output_file = 'pose_data_含時間.csv'
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n轉換完成！")
        print(f"影片幀率: {fps:.2f} FPS")
        print(f"總幀數: {len(df)}")
        print(f"總時長: {df['時間_秒'].max():.2f} 秒 ({df['時間_分秒'].iloc[-1]})")
        print(f"新檔案已保存為: {output_file}")
        
        # 顯示前幾行作為範例
        print("\n前5行資料範例:")
        print(df[[frame_column, '時間_秒', '時間_分秒']].head())
        
    except Exception as e:
        print(f"處理過程中發生錯誤: {e}")

if __name__ == "__main__":
    add_time_to_csv()
