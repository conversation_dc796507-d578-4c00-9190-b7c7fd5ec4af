
SOP姿態特徵詳細定義說明手冊
================================================================================
生成時間：2025年07月10日 17:15:47
================================================================================

本手冊詳細說明每個特徵的計算方式、數值含義、正常範圍及判斷標準。

================================================================================
1. 基本資訊欄位
================================================================================

【幀數】
• 定義：影片中的第幾幀
• 數值範圍：0 到總幀數
• 範例：0, 1, 2, 3...
• 說明：用於標識時間順序

【時間_秒】
• 定義：對應的時間點（秒）
• 計算方式：幀數 ÷ 影片幀率
• 數值範圍：0.0 到影片總時長
• 範例：0.000, 0.033, 0.067...
• 說明：方便與實際時間對應

【標籤】
• 定義：人工標記的SOP步驟
• 數值類型：文字或數字
• 範例：空白（未標記）、"拿螺絲"、"定位"、"鎖緊"
• 說明：用於訓練機器學習模型

================================================================================
2. 角度特徵（單位：度）
================================================================================

【左手肘角度 / 右手肘角度】
• 定義：肩膀-手肘-手腕三點形成的角度
• 計算方式：使用向量夾角公式計算
• 數值範圍：0° - 180°
• 正常範圍：90° - 150°
• 範例數值：151.82° (略微過度伸展)
• 判斷標準：
  - < 90°：手臂過度彎曲
  - 90° - 150°：正常工作角度 ✓
  - > 150°：手臂過度伸展

【左手腕角度 / 右手腕角度】
• 定義：手腕相對於手肘-肩膀參考線的旋轉角度
• 計算方式：計算手腕向量與參考向量的夾角
• 數值範圍：0° - 180°
• 旋轉閾值：> 15°
• 範例數值：162.35° (檢測到明顯旋轉)
• 判斷標準：
  - > 15°：檢測到旋轉動作 ✓
  - ≤ 15°：無明顯旋轉

================================================================================
3. 距離特徵（標準化單位：0-1）
================================================================================

【左臂伸展程度 / 右臂伸展程度】
• 定義：手腕到肩膀的3D直線距離
• 計算方式：√[(x₂-x₁)² + (y₂-y₁)² + (z₂-z₁)²]
• 數值範圍：0.0 - 1.0+
• 範例數值：0.500 (中等伸展)
• 說明：
  - 0.3 - 0.4：手臂收縮
  - 0.4 - 0.6：正常工作範圍
  - 0.6+：手臂完全伸展

【雙手距離】
• 定義：左右手腕之間的3D距離
• 計算方式：兩手腕座標的歐幾里得距離
• 數值範圍：0.0 - 1.5+
• 範例數值：0.597 (雙手適中距離)
• 說明：
  - < 0.3：雙手靠近
  - 0.3 - 0.8：正常工作距離
  - > 0.8：雙手分開較遠

================================================================================
4. 速度特徵（標準化單位/秒）
================================================================================

【左手腕速度 / 右手腕速度】
• 定義：手腕移動的瞬時速度大小
• 計算方式：√[vₓ² + vᵧ² + vᵤ²]，其中v = Δ座標/Δ時間
• 數值範圍：0.0 - 無上限
• 正常範圍：0.01 - 0.5
• 範例數值：2.009 (速度過快)
• 判斷標準：
  - < 0.01：幾乎靜止
  - 0.01 - 0.5：正常工作速度 ✓
  - > 0.5：動作過快
• 實際含義：
  - 0.1 ≈ 每秒移動10%畫面寬度
  - 1.0 ≈ 每秒橫跨整個畫面
  - 2.0 ≈ 每秒橫跨畫面2次

================================================================================
5. 軌跡與穩定性特徵
================================================================================

【左手軌跡規律性 / 右手軌跡規律性】
• 定義：手部移動軌跡的規律程度
• 計算方式：1/(1+軌跡距離標準差)
• 數值範圍：0.0 - 1.0
• 正常閾值：> 0.3
• 範例數值：0 (資料不足或不規律)
• 說明：
  - 0：完全不規律或資料不足
  - 0.3 - 0.7：中等規律性
  - 0.7+：高度規律性

【左手腕穩定性 / 右手腕穩定性】
• 定義：手腕速度的穩定程度（5幀滑動窗口）
• 計算方式：速度的滾動標準差
• 數值範圍：0.0 - 無上限
• 範例數值：空白 (前幾幀資料不足)
• 說明：
  - 數值越小 = 越穩定
  - 空白 = 計算窗口不足

================================================================================
6. 姿勢特徵
================================================================================

【肩膀水平度】
• 定義：左右肩膀Y座標差的絕對值
• 計算方式：|左肩Y座標 - 右肩Y座標|
• 數值範圍：0.0 - 1.0
• 正常標準：< 0.1
• 範例數值：0.206 (肩膀傾斜)
• 判斷標準：
  - < 0.1：肩膀水平 ✓
  - ≥ 0.1：肩膀傾斜

【身體前傾程度】
• 定義：肩膀中心與臀部中心X座標差的絕對值
• 計算方式：|肩膀中心X - 臀部中心X|
• 數值範圍：0.0 - 1.0
• 正常標準：< 0.2
• 範例數值：0.184 (輕微前傾，正常)
• 判斷標準：
  - < 0.2：正常姿勢 ✓
  - ≥ 0.2：過度前傾

【頭部位置】
• 定義：鼻子相對於肩膀中心的Y座標差
• 計算方式：鼻子Y座標 - 肩膀中心Y座標
• 數值範圍：-1.0 - 1.0
• 正常標準：> -0.3
• 範例數值：0.063 (頭部位置正常)
• 判斷標準：
  - > -0.3：頭部位置正常 ✓
  - ≤ -0.3：過度低頭

================================================================================
7. 手指特徵
================================================================================

【左手手指張開度 / 右手手指張開度】
• 定義：手指間距離的平均值
• 計算方式：(拇指-食指距離 + 食指-小指距離) / 2
• 數值範圍：0.0 - 1.0
• 範例數值：0.060 (手指較為收攏)
• 說明：
  - < 0.05：手指緊握
  - 0.05 - 0.15：正常握持
  - > 0.15：手指張開

================================================================================
8. 工作節奏特徵
================================================================================

【左手動作頻率 / 右手動作頻率】
• 定義：每分鐘檢測到的動作峰值次數
• 計算方式：(峰值數量 / 總時間) × 60
• 數值範圍：0 - 無上限
• 正常範圍：10 - 60 次/分鐘
• 範例數值：447.3 (頻率異常高)
• 判斷標準：
  - 10 - 60：正常工作頻率 ✓
  - < 10：動作過慢
  - > 60：動作過快

【左手停頓時間 / 右手停頓時間】
• 定義：連續低速狀態的持續時間
• 計算方式：速度 < 0.05 的連續時間
• 數值範圍：0.0 - 無上限
• 正常標準：< 5.0 秒
• 範例數值：0 (無停頓)
• 判斷標準：
  - < 5.0：正常停頓 ✓
  - ≥ 5.0：停頓過久

【工作節奏】
• 定義：當前幀是否為動作峰值
• 數值類型：0 或 1
• 範例數值：0 (非峰值幀)
• 說明：
  - 0：非動作峰值
  - 1：檢測到動作峰值

================================================================================
9. SOP合規性檢查（布林值）
================================================================================

【手肘角度正常】
• 檢查條件：左右手肘角度都在 90° - 150° 範圍內
• 範例：FALSE (左手肘151.82° > 150°)

【手腕旋轉正常】
• 檢查條件：左手腕角度 > 15° 或 右手腕角度 > 15°
• 範例：TRUE (兩個角度都 > 15°)

【動作速度正常】
• 檢查條件：右手腕速度在 0.01 - 0.5 範圍內
• 範例：FALSE (右手腕速度2.009 > 0.5)

【軌跡規律正常】
• 檢查條件：左手或右手軌跡規律性 > 0.3
• 範例：FALSE (兩手軌跡規律性都為0)

【姿勢正常】
• 檢查條件：肩膀水平度 < 0.1 且 身體前傾程度 < 0.2
• 範例：FALSE (肩膀水平度0.206 > 0.1)

【頭部位置正常】
• 檢查條件：頭部位置 > -0.3
• 範例：TRUE (頭部位置0.063 > -0.3)

【動作頻率正常】
• 檢查條件：右手動作頻率在 10 - 60 次/分鐘範圍內
• 範例：FALSE (右手頻率473.3 > 60)

【停頓時間正常】
• 檢查條件：右手停頓時間 < 5.0 秒
• 範例：TRUE (右手停頓時間0 < 5.0)

【SOP合規性評分】
• 定義：所有檢查項目的通過比例
• 計算方式：通過項目數 ÷ 總檢查項目數
• 數值範圍：0.0 - 1.0
• 範例數值：0.375 (8項中3項通過 = 3/8)
• 評分等級：
  - 0.8 - 1.0：優秀
  - 0.6 - 0.8：良好
  - 0.4 - 0.6：普通
  - 0.0 - 0.4：需改進

================================================================================
10. 數值解讀範例
================================================================================

以您提供的數據為例：
151.82 | 130.45 | 162.35 | 128.63 | 0.500 | 0.480 | 0.402 | 2.009 | ...

解讀：
• 左手肘角度151.82°：略微過度伸展（超出150°上限）
• 右手肘角度130.45°：正常範圍內
• 左手腕角度162.35°：檢測到明顯旋轉動作
• 右手腕角度128.63°：檢測到旋轉動作
• 左臂伸展0.500：中等伸展程度
• 右臂伸展0.480：中等伸展程度
• 左手腕速度0.402：接近正常上限
• 右手腕速度2.009：明顯過快（超出0.5上限4倍）

最終SOP評分0.375：表示8項檢查中只有3項通過，需要改進。

================================================================================
注意事項
================================================================================

1. 所有座標都是標準化的（0-1範圍），不是真實物理尺寸
2. 速度單位是"標準化座標/秒"，需要結合實際畫面尺寸理解
3. 閾值可能需要根據實際工作環境調整
4. 空白值通常表示計算所需的歷史資料不足
5. TRUE/FALSE是基於預設閾值的自動判斷，可能需要人工驗證

================================================================================
