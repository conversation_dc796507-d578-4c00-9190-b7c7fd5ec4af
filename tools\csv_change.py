import pandas as pd
import numpy as np
import math

class SOPFeatureExtractor:
    def __init__(self):
        # 定義重要的關鍵點索引（根據你的CSV欄位）
        self.keypoints = {
            'nose': 0,
            'left_shoulder': 11, 'right_shoulder': 12,
            'left_elbow': 13, 'right_elbow': 14,
            'left_wrist': 15, 'right_wrist': 16,
            'left_pinky': 17, 'right_pinky': 18,
            'left_index': 19, 'right_index': 20,
            'left_thumb': 21, 'right_thumb': 22,
            'left_hip': 23, 'right_hip': 24
        }
    
    def load_csv(self, file_path):
        """讀取CSV檔案"""
        df = pd.read_csv(file_path)
        return df
    
    def extract_coordinates(self, df):
        """提取關鍵點座標"""
        coords = {}
        
        # 提取每個關鍵點的XYZ座標
        points = ['鼻子', '左肩膀', '右肩膀', '左手肘', '右手肘', 
                 '左手腕', '右手腕', '左手小指', '右手小指', 
                 '左手食指', '右手食指', '左手拇指', '右手拇指',
                 '左臀部', '右臀部']
        
        for point in points:
            coords[point] = {
                'x': df[f'{point}_X座標'].values,
                'y': df[f'{point}_Y座標'].values,
                'z': df[f'{point}_Z座標'].values,
                'visibility': df[f'{point}_可見度'].values
            }
        
        return coords
    
    def calculate_distance(self, p1, p2):
        """計算兩點間的3D距離"""
        return np.sqrt((p1['x'] - p2['x'])**2 + 
                      (p1['y'] - p2['y'])**2 + 
                      (p1['z'] - p2['z'])**2)
    
    def calculate_angle(self, p1, p2, p3):
        """計算三點形成的角度"""
        # 向量 p2->p1 和 p2->p3
        v1 = np.array([p1['x'] - p2['x'], p1['y'] - p2['y'], p1['z'] - p2['z']])
        v2 = np.array([p3['x'] - p2['x'], p3['y'] - p2['y'], p3['z'] - p2['z']])
        
        # 計算角度
        cos_angle = np.sum(v1 * v2, axis=0) / (np.linalg.norm(v1, axis=0) * np.linalg.norm(v2, axis=0))
        angles = np.arccos(np.clip(cos_angle, -1.0, 1.0)) * 180 / np.pi
        
        return angles
    
    def calculate_wrist_angle(self, wrist, elbow, shoulder):
        """計算手腕旋轉角度（用於檢測螺絲旋轉動作）"""
        # 計算手腕在XY平面的旋轉角度
        wrist_vector = np.array([wrist['x'] - elbow['x'], wrist['y'] - elbow['y']])
        reference_vector = np.array([shoulder['x'] - elbow['x'], shoulder['y'] - elbow['y']])
        
        # 計算兩向量的角度
        dot_product = np.sum(wrist_vector * reference_vector, axis=0)
        norms = np.linalg.norm(wrist_vector, axis=0) * np.linalg.norm(reference_vector, axis=0)
        cos_angle = dot_product / (norms + 1e-8)  # 避免除零
        
        angles = np.arccos(np.clip(cos_angle, -1.0, 1.0)) * 180 / np.pi
        return angles
    
    def calculate_trajectory_regularity(self, coords_history, window_size=10):
        """計算手部軌跡規律性"""
        if len(coords_history) < window_size:
            return np.zeros(len(coords_history))
        
        regularity_scores = []
        
        for i in range(len(coords_history)):
            if i < window_size:
                regularity_scores.append(0)
                continue
                
            # 取最近的軌跡段
            recent_trajectory = coords_history[i-window_size:i]
            
            # 計算軌跡平滑度（相鄰點間距離的標準差）
            distances = []
            for j in range(1, len(recent_trajectory)):
                dist = np.sqrt(
                    (recent_trajectory[j]['x'] - recent_trajectory[j-1]['x'])**2 +
                    (recent_trajectory[j]['y'] - recent_trajectory[j-1]['y'])**2 +
                    (recent_trajectory[j]['z'] - recent_trajectory[j-1]['z'])**2
                )
                distances.append(dist)
            
            # 標準差越小表示越規律
            regularity = 1 / (1 + np.std(distances)) if distances else 0
            regularity_scores.append(regularity)
        
        return np.array(regularity_scores)
    
    def calculate_action_frequency(self, speed_data, time_data, threshold=0.1):
        """計算動作頻率（每分鐘動作次數）"""
        # 檢測速度峰值
        peaks = []
        for i in range(1, len(speed_data)-1):
            if (speed_data.iloc[i] > speed_data.iloc[i-1] and 
                speed_data.iloc[i] > speed_data.iloc[i+1] and 
                speed_data.iloc[i] > threshold):
                peaks.append(i)
        
        if len(peaks) < 2:
            return np.zeros(len(speed_data))
        
        # 計算每分鐘頻率
        total_time = time_data.iloc[-1] - time_data.iloc[0]  # 總時間（秒）
        if total_time <= 0:
            return np.zeros(len(speed_data))
        
        frequency_per_minute = (len(peaks) / total_time) * 60
        
        # 為每個時間點分配頻率值
        frequency_array = np.full(len(speed_data), frequency_per_minute)
        
        return frequency_array
    
    def calculate_pause_time(self, speed_data, time_data, speed_threshold=0.05):
        """計算停頓時間"""
        pause_times = []
        current_pause_start = None
        
        for i in range(len(speed_data)):
            if speed_data.iloc[i] < speed_threshold:
                # 開始停頓
                if current_pause_start is None:
                    current_pause_start = time_data.iloc[i]
                pause_times.append(0)  # 暫時設為0
            else:
                # 結束停頓
                if current_pause_start is not None:
                    pause_duration = time_data.iloc[i] - current_pause_start
                    # 回填停頓時間
                    start_idx = time_data[time_data >= current_pause_start].index[0]
                    for j in range(start_idx, i):
                        if j < len(pause_times):
                            pause_times[j] = pause_duration
                    current_pause_start = None
                pause_times.append(0)
        
        return np.array(pause_times[:len(speed_data)])
    
    def calculate_velocity(self, coords, time_data):
        """計算速度"""
        velocity = {}
        # 計算實際的時間差
        time_diff = np.gradient(time_data)

        for point, data in coords.items():
            vx = np.gradient(data['x']) / time_diff
            vy = np.gradient(data['y']) / time_diff
            vz = np.gradient(data['z']) / time_diff
            velocity[point] = {
                'vx': vx, 'vy': vy, 'vz': vz,
                'magnitude': np.sqrt(vx**2 + vy**2 + vz**2)
            }
        return velocity
    
    def extract_sop_features(self, df):
        """提取SOP監控相關特徵"""
        coords = self.extract_coordinates(df)
        # 傳入時間資料來計算實際速度
        velocity = self.calculate_velocity(coords, df['時間_秒'].values)
        
        features = pd.DataFrame()
        
        # 基本資訊
        features['frame'] = df['幀數']
        features['time'] = df['時間_秒']
        features['label'] = df['標籤'] if '標籤' in df.columns else 0
        
        # 1. 手部關鍵特徵（鎖螺絲動作）
        # 肘關節角度
        features['left_elbow_angle'] = self.calculate_angle(
            coords['左肩膀'], coords['左手肘'], coords['左手腕']
        )
        features['right_elbow_angle'] = self.calculate_angle(
            coords['右肩膀'], coords['右手肘'], coords['右手腕']
        )
        
        # ** 新增：手腕角度（螺絲旋轉檢測）**
        features['left_wrist_angle'] = self.calculate_wrist_angle(
            coords['左手腕'], coords['左手肘'], coords['左肩膀']
        )
        features['right_wrist_angle'] = self.calculate_wrist_angle(
            coords['右手腕'], coords['右手肘'], coords['右肩膀']
        )
        
        # 手腕到肩膀距離（手臂伸展程度）
        features['left_arm_extension'] = self.calculate_distance(
            coords['左手腕'], coords['左肩膀']
        )
        features['right_arm_extension'] = self.calculate_distance(
            coords['右手腕'], coords['右肩膀']
        )
        
        # 2. 手部動作速度（檢測旋轉動作）
        features['left_wrist_speed'] = velocity['左手腕']['magnitude']
        features['right_wrist_speed'] = velocity['右手腕']['magnitude']
        
        # ** 新增：手部軌跡規律性 **
        left_wrist_trajectory = [{'x': coords['左手腕']['x'][i], 
                                 'y': coords['左手腕']['y'][i], 
                                 'z': coords['左手腕']['z'][i]} 
                                for i in range(len(coords['左手腕']['x']))]
        right_wrist_trajectory = [{'x': coords['右手腕']['x'][i], 
                                  'y': coords['右手腕']['y'][i], 
                                  'z': coords['右手腕']['z'][i]} 
                                 for i in range(len(coords['右手腕']['x']))]
        
        features['left_trajectory_regularity'] = self.calculate_trajectory_regularity(left_wrist_trajectory)
        features['right_trajectory_regularity'] = self.calculate_trajectory_regularity(right_wrist_trajectory)
        
        # 3. 雙手協調性
        features['hands_distance'] = self.calculate_distance(
            coords['左手腕'], coords['右手腕']
        )
        
        # 4. 工作姿勢特徵
        # 肩膀水平度（檢測是否傾斜）
        features['shoulder_level'] = abs(coords['左肩膀']['y'] - coords['右肩膀']['y'])
        
        # 身體前傾程度
        body_center_x = (coords['左肩膀']['x'] + coords['右肩膀']['x']) / 2
        hip_center_x = (coords['左臀部']['x'] + coords['右臀部']['x']) / 2
        features['body_lean'] = abs(body_center_x - hip_center_x)
        
        # ** 新增：頭部位置 **
        # 頭部相對於肩膀的位置
        shoulder_center_y = (coords['左肩膀']['y'] + coords['右肩膀']['y']) / 2
        features['head_position'] = coords['鼻子']['y'] - shoulder_center_y  # 負值表示低頭
        
        # 5. 手指精細動作（如果需要檢測握持動作）
        # 手指到手腕距離
        features['left_finger_spread'] = (
            self.calculate_distance(coords['左手拇指'], coords['左手食指']) +
            self.calculate_distance(coords['左手食指'], coords['左手小指'])
        ) / 2
        
        features['right_finger_spread'] = (
            self.calculate_distance(coords['右手拇指'], coords['右手食指']) +
            self.calculate_distance(coords['右手食指'], coords['右手小指'])
        ) / 2
        
        # 6. 動作穩定性
        # 計算移動平均來檢測動作穩定性
        window_size = 5
        features['left_wrist_stability'] = features['left_wrist_speed'].rolling(window=window_size).std()
        features['right_wrist_stability'] = features['right_wrist_speed'].rolling(window=window_size).std()
        
        # 7. 動作節奏特徵
        # ** 新增：動作頻率 **
        features['left_action_frequency'] = self.calculate_action_frequency(
            features['left_wrist_speed'], features['time']
        )
        features['right_action_frequency'] = self.calculate_action_frequency(
            features['right_wrist_speed'], features['time']
        )
        
        # ** 新增：停頓時間 **
        features['left_pause_time'] = self.calculate_pause_time(
            features['left_wrist_speed'], features['time']
        )
        features['right_pause_time'] = self.calculate_pause_time(
            features['right_wrist_speed'], features['time']
        )
        
        # 檢測動作週期性
        features['work_rhythm'] = self.detect_work_cycles(features['right_wrist_speed'])
        
        return features
    
    def detect_work_cycles(self, speed_data, threshold=0.1):
        """檢測工作週期"""
        # 簡單的峰值檢測來識別重複動作
        cycles = np.zeros(len(speed_data))
        
        for i in range(1, len(speed_data)-1):
            if (speed_data.iloc[i] > speed_data.iloc[i-1] and 
                speed_data.iloc[i] > speed_data.iloc[i+1] and 
                speed_data.iloc[i] > threshold):
                cycles[i] = 1
        
        return cycles
    
    def analyze_sop_compliance(self, features):
        """分析SOP合規性"""
        analysis = {}
        
        # 1. 手臂角度檢查（正常工作範圍）
        analysis['elbow_angle_ok'] = (
            (features['left_elbow_angle'] > 90) & (features['left_elbow_angle'] < 150) &
            (features['right_elbow_angle'] > 90) & (features['right_elbow_angle'] < 150)
        )
        
        # 2. 手腕角度檢查（螺絲旋轉檢測）
        analysis['wrist_rotation_ok'] = (
            (features['left_wrist_angle'] > 15) | (features['right_wrist_angle'] > 15)
        )
        
        # 3. 動作速度檢查（不要太快或太慢）
        analysis['speed_ok'] = (
            (features['right_wrist_speed'] > 0.01) & (features['right_wrist_speed'] < 0.5)
        )
        
        # 4. 軌跡規律性檢查
        analysis['trajectory_ok'] = (
            (features['left_trajectory_regularity'] > 0.3) | 
            (features['right_trajectory_regularity'] > 0.3)
        )
        
        # 5. 姿勢穩定性檢查
        analysis['posture_ok'] = (
            (features['shoulder_level'] < 0.1) & (features['body_lean'] < 0.2)
        )
        
        # 6. 頭部位置檢查（避免過度低頭）
        analysis['head_position_ok'] = (
            features['head_position'] > -0.3  # 不要低頭超過30cm
        )
        
        # 7. 動作頻率檢查（合理範圍）
        analysis['frequency_ok'] = (
            (features['right_action_frequency'] > 10) & 
            (features['right_action_frequency'] < 60)  # 每分鐘10-60次
        )
        
        # 8. 停頓時間檢查（不要停頓太久）
        analysis['pause_time_ok'] = (
            features['right_pause_time'] < 5.0  # 停頓不超過5秒
        )
        
        # 9. 整體合規性評分
        analysis['compliance_score'] = (
            analysis['elbow_angle_ok'].astype(int) +
            analysis['wrist_rotation_ok'].astype(int) +
            analysis['speed_ok'].astype(int) +
            analysis['trajectory_ok'].astype(int) +
            analysis['posture_ok'].astype(int) +
            analysis['head_position_ok'].astype(int) +
            analysis['frequency_ok'].astype(int) +
            analysis['pause_time_ok'].astype(int)
        ) / 8
        
        return analysis

# 使用範例
def main():
    # 初始化特徵提取器
    extractor = SOPFeatureExtractor()

    # 讀取你的CSV檔案
    df = extractor.load_csv('pose_data_含時間.csv')  # 使用實際的檔案路徑

    # 提取SOP監控特徵
    print("正在提取SOP監控特徵...")
    features = extractor.extract_sop_features(df)

    # 分析合規性
    print("正在分析合規性...")
    compliance = extractor.analyze_sop_compliance(features)

    # 將合規性分析加入特徵
    for key, value in compliance.items():
        features[key] = value

    # 保存轉換後的特徵
    features.to_csv('sop_features.csv', index=False)
    print("特徵提取完成！已保存為 'sop_features.csv'")
    print(f"總共提取了 {len(features)} 行資料，{len(features.columns)} 個特徵")

if __name__ == "__main__":
    main()