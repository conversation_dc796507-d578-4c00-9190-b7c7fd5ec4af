Stack trace:
Frame         Function      Args
0007FFFFBF30  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBF30, 0007FFFFAE30) msys-2.0.dll+0x2118E
0007FFFFBF30  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFFBF30  0002100469F2 (00021028DF99, 0007FFFFBDE8, 0007FFFFBF30, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBF30  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBF30  00021006A545 (0007FFFFBF40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBF40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFC4780000 ntdll.dll
7FFFC2AC0000 KERNEL32.DLL
7FFFC1BB0000 KERNELBASE.dll
7FFFC4440000 USER32.dll
7FFFC1A60000 win32u.dll
7FFFC3670000 GDI32.dll
7FFFC1780000 gdi32full.dll
7FFFC2740000 msvcp_win.dll
7FFFC1A90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFC42F0000 advapi32.dll
7FFFC43A0000 msvcrt.dll
7FFFC2B80000 sechost.dll
7FFFC41D0000 RPCRT4.dll
7FFFC1040000 CRYPTBASE.DLL
7FFFC16F0000 bcryptPrimitives.dll
7FFFC39F0000 IMM32.DLL
