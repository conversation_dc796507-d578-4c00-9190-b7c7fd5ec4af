import pandas as pd

def convert_features_to_chinese():
    """將SOP特徵CSV檔案的欄位名稱轉換為繁體中文"""
    
    # 定義英文到繁體中文的欄位對應表
    chinese_headers = {
        'frame': '幀數',
        'time': '時間_秒',
        'label': '標籤',
        'left_elbow_angle': '左手肘角度',
        'right_elbow_angle': '右手肘角度',
        'left_wrist_angle': '左手腕角度',
        'right_wrist_angle': '右手腕角度',
        'left_arm_extension': '左臂伸展程度',
        'right_arm_extension': '右臂伸展程度',
        'left_wrist_speed': '左手腕速度',
        'right_wrist_speed': '右手腕速度',
        'left_trajectory_regularity': '左手軌跡規律性',
        'right_trajectory_regularity': '右手軌跡規律性',
        'hands_distance': '雙手距離',
        'shoulder_level': '肩膀水平度',
        'body_lean': '身體前傾程度',
        'head_position': '頭部位置',
        'left_finger_spread': '左手手指張開度',
        'right_finger_spread': '右手手指張開度',
        'left_wrist_stability': '左手腕穩定性',
        'right_wrist_stability': '右手腕穩定性',
        'left_action_frequency': '左手動作頻率',
        'right_action_frequency': '右手動作頻率',
        'left_pause_time': '左手停頓時間',
        'right_pause_time': '右手停頓時間',
        'work_rhythm': '工作節奏',
        'elbow_angle_ok': '手肘角度正常',
        'wrist_rotation_ok': '手腕旋轉正常',
        'speed_ok': '動作速度正常',
        'trajectory_ok': '軌跡規律正常',
        'posture_ok': '姿勢正常',
        'head_position_ok': '頭部位置正常',
        'frequency_ok': '動作頻率正常',
        'pause_time_ok': '停頓時間正常',
        'compliance_score': 'SOP合規性評分'
    }
    
    try:
        # 讀取特徵CSV檔案
        print("正在讀取特徵CSV檔案...")
        df = pd.read_csv('sop_features.csv', encoding='utf-8')
        
        # 重新命名欄位
        print("正在轉換欄位名稱為繁體中文...")
        df.rename(columns=chinese_headers, inplace=True)
        
        # 保存為新的CSV檔案，使用UTF-8編碼並加上BOM
        print("正在保存新的CSV檔案...")
        df.to_csv('sop_features_繁體中文.csv', index=False, encoding='utf-8-sig')
        
        print("轉換完成！新檔案已保存為 'sop_features_繁體中文.csv'")
        print(f"總共處理了 {len(df)} 行資料")
        print(f"特徵數量: {len(df.columns)} 個")
        print("\n前5個欄位名稱：")
        for i, col in enumerate(df.columns[:5]):
            print(f"  {i+1}. {col}")
        
        print("\n後5個欄位名稱：")
        for i, col in enumerate(df.columns[-5:]):
            print(f"  {len(df.columns)-4+i}. {col}")
        
    except FileNotFoundError:
        print("找不到 'sop_features.csv' 檔案")
        print("請確認檔案是否存在於當前目錄中")
        print("您可能需要先運行 'csv_change.py' 來生成特徵檔案")
    except Exception as e:
        print(f"轉換過程中發生錯誤：{e}")

if __name__ == "__main__":
    convert_features_to_chinese()
